// 水印处理工具函数
export async function addWatermarkToImage(imageUrl: string): Promise<string> {
  return new Promise((resolve, reject) => {
    console.log('🖼️ Loading image for watermark:', imageUrl.substring(0, 50) + '...');

    // 检查是否需要使用代理 - 对于 img.kontext-dev.com 的图片总是使用代理
    const needsProxy = imageUrl.startsWith('https://img.kontext-dev.com/');

    let finalImageUrl = imageUrl;
    if (needsProxy) {
      // 使用代理API来避免跨域问题
      finalImageUrl = `/api/proxy-image?url=${encodeURIComponent(imageUrl)}`;
      console.log('🔄 Using proxy for cross-origin image:', finalImageUrl);
    }

    const img = new Image();

    // 设置跨域策略
    if (!needsProxy) {
      img.crossOrigin = 'anonymous';
    }

    img.onload = function() {
      console.log('✅ Image loaded successfully, dimensions:', img.width, 'x', img.height);
      try {
        // Create canvas with same dimensions as original image
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');

        if (!ctx) {
          reject(new Error('Failed to get canvas context'));
          return;
        }

        canvas.width = img.width;
        canvas.height = img.height;

        // Draw original image
        ctx.drawImage(img, 0, 0);
        console.log('✅ Original image drawn to canvas');

        // Add watermark
        addWatermarkToCanvas(ctx, canvas.width, canvas.height, 'kontext-dev.com');
        console.log('✅ Watermark added to canvas');

        // Convert to data URL
        const watermarkedData = canvas.toDataURL('image/png');
        console.log('✅ Canvas converted to data URL, length:', watermarkedData.length);
        resolve(watermarkedData);
      } catch (error) {
        console.error('❌ Error processing image in canvas:', error);
        reject(error);
      }
    };

    img.onerror = (error) => {
      console.error('❌ Failed to load image:', error);

      if (!needsProxy) {
        console.log('🔄 Trying with proxy API...');
        // 如果直接加载失败，尝试使用代理
        const proxyUrl = `/api/proxy-image?url=${encodeURIComponent(imageUrl)}`;
        const img2 = new Image();
        img2.onload = function() {
          console.log('✅ Image loaded via proxy');
          try {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');

            if (!ctx) {
              reject(new Error('Failed to get canvas context'));
              return;
            }

            canvas.width = img2.width;
            canvas.height = img2.height;
            ctx.drawImage(img2, 0, 0);
            addWatermarkToCanvas(ctx, canvas.width, canvas.height, 'kontext-dev.com');
            const watermarkedData = canvas.toDataURL('image/png');
            resolve(watermarkedData);
          } catch (canvasError) {
            console.error('❌ Canvas processing failed:', canvasError);
            reject(canvasError);
          }
        };
        img2.onerror = () => reject(new Error('Failed to load image even with proxy'));
        img2.src = proxyUrl;
      } else {
        // 如果代理也失败了，尝试直接加载（设置 crossOrigin）
        console.log('🔄 Proxy failed, trying direct load with CORS...');
        const img3 = new Image();
        img3.crossOrigin = 'anonymous';
        img3.onload = function() {
          console.log('✅ Image loaded directly with CORS');
          try {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');

            if (!ctx) {
              reject(new Error('Failed to get canvas context'));
              return;
            }

            canvas.width = img3.width;
            canvas.height = img3.height;
            ctx.drawImage(img3, 0, 0);
            addWatermarkToCanvas(ctx, canvas.width, canvas.height, 'kontext-dev.com');
            const watermarkedData = canvas.toDataURL('image/png');
            resolve(watermarkedData);
          } catch (canvasError) {
            console.error('❌ Canvas processing failed:', canvasError);
            reject(canvasError);
          }
        };
        img3.onerror = () => reject(new Error('Failed to load image with all methods'));
        img3.src = imageUrl;
      }
    };

    img.src = finalImageUrl;
  });
}

function addWatermarkToCanvas(ctx: CanvasRenderingContext2D, width: number, height: number, text: string) {
  // Save current state
  ctx.save();

  // Set watermark style with adaptive font size
  const fontSize = Math.max(width, height) * 0.025; // Slightly smaller for consistency
  ctx.font = `bold ${fontSize}px Arial`;
  ctx.fillStyle = 'rgba(255, 255, 255, 0.7)';
  ctx.strokeStyle = 'rgba(0, 0, 0, 0.5)';
  ctx.lineWidth = 2;
  ctx.textAlign = 'center';
  ctx.textBaseline = 'middle';

  // Fixed density approach - consistent spacing regardless of image size
  const baseSpacing = 300; // Increased base spacing for less density
  const spacingX = Math.max(baseSpacing, width * 0.25);  // 25% of width, minimum 300px
  const spacingY = Math.max(baseSpacing * 0.7, height * 0.2); // 20% of height, minimum 210px

  // Rotation angle (-45 degrees)
  const angle = -Math.PI / 4;

  // Calculate grid based on image dimensions, not diagonal
  const cols = Math.ceil(width / spacingX) + 3;
  const rows = Math.ceil(height / spacingY) + 3;

  // Calculate starting offset to center the pattern
  const startX = -(cols * spacingX - width) / 2;
  const startY = -(rows * spacingY - height) / 2;

  // Add watermarks in grid pattern
  for (let row = 0; row < rows; row++) {
    for (let col = 0; col < cols; col++) {
      ctx.save();
      
      const x = startX + col * spacingX;
      const y = startY + row * spacingY;
      
      // Only draw if the watermark center is within reasonable bounds
      if (x > -spacingX && x < width + spacingX && 
          y > -spacingY && y < height + spacingY) {
        
        ctx.translate(x, y);
        ctx.rotate(angle);
        
        ctx.strokeText(text, 0, 0);
        ctx.fillText(text, 0, 0);
      }
      
      ctx.restore();
    }
  }

  // Restore state
  ctx.restore();
}

// 检查用户积分并决定是否需要添加水印
export async function shouldAddWatermark(): Promise<boolean> {
  try {
    console.log('🔍 Checking user credits for watermark...');
    const response = await fetch('/api/credits');
    if (response.ok) {
      const userCredits = await response.json();
      console.log('💰 User credits:', userCredits.left_credits);
      const needsWatermark = userCredits.left_credits <= 5;
      console.log('🎨 Needs watermark:', needsWatermark);
      return needsWatermark;
    }
    console.warn('⚠️ Credits API response not ok, defaulting to add watermark');
    return true; // 如果获取积分失败，默认添加水印
  } catch (error) {
    console.error('❌ Error checking user credits:', error);
    return true; // 如果获取积分失败，默认添加水印
  }
}

// 处理图片数组，根据用户积分添加水印，并保存到数据库
export async function processImagesWithWatermark(imageUrls: string[], taskId?: string): Promise<string[]> {
  console.log('🖼️ Processing images with watermark check...', imageUrls.length, 'images');
  const needsWatermark = await shouldAddWatermark();

  if (!needsWatermark) {
    console.log('✅ User has sufficient credits, returning original images');
    return imageUrls; // 用户积分足够，返回原图
  }

  console.log('🎨 User needs watermark, processing images...');

  // 串行处理图片，确保数据库更新的顺序
  const watermarkedImages: string[] = [];

  for (let index = 0; index < imageUrls.length; index++) {
    const imageUrl = imageUrls[index];
    try {
      console.log(`🔄 Adding watermark to image ${index + 1}/${imageUrls.length}:`, imageUrl.substring(0, 50) + '...');
      const watermarkedImage = await addWatermarkToImage(imageUrl);
      console.log(`✅ Watermark added to image ${index + 1}`);

      // 如果有taskId，上传水印图片到数据库
      if (taskId) {
        try {
          console.log(`💾 Uploading watermarked image ${index + 1} to database...`);
          const uploadResponse = await fetch('/api/upload-watermarked-image', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              imageData: watermarkedImage,
              taskId: taskId,
              originalUrl: imageUrl,
              imageIndex: index
            }),
          });

          if (uploadResponse.ok) {
            const result = await uploadResponse.json();
            console.log(`✅ Watermarked image ${index + 1} uploaded to database:`, result.watermarkedUrl);
            // 返回数据库中的URL而不是base64
            watermarkedImages.push(result.watermarkedUrl);
          } else {
            const errorText = await uploadResponse.text();
            console.error(`❌ Failed to upload watermarked image ${index + 1} to database:`, errorText);
            // 如果上传失败，使用base64作为备用
            watermarkedImages.push(watermarkedImage);
          }
        } catch (uploadError) {
          console.error(`❌ Error uploading watermarked image ${index + 1}:`, uploadError);
          // 如果上传失败，使用base64作为备用
          watermarkedImages.push(watermarkedImage);
        }
      } else {
        // 没有taskId，直接返回base64
        watermarkedImages.push(watermarkedImage);
      }

    } catch (error) {
      console.error(`❌ Error adding watermark to image ${index + 1}:`, error);
      watermarkedImages.push(imageUrl); // 如果添加水印失败，返回原图
    }
  }

  console.log('🎉 Watermark processing and upload completed');
  return watermarkedImages;
}
