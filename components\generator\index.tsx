"use client";

import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Upload } from "lucide-react";
import Image from "next/image";
import { useState } from "react";
import { useSession } from "next-auth/react";
import { toast } from "sonner";
import { useRouter } from "next/navigation";

export const Generator = () => {
  const { data: session } = useSession();
  const router = useRouter();
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [result, setResult] = useState<{
    description: string;
    prompt: string;
    originalImage: string;
    generatedImage: string;
  } | null>(null);
  const [error, setError] = useState<string | null>(null);

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      if (!file.type.startsWith('image/')) {
        setError('Please upload an image file');
        return;
      }

      const reader = new FileReader();
      reader.onloadend = () => {
        const result = reader.result as string;
        if (typeof result === 'string' && result.startsWith('data:image/')) {
          setSelectedImage(result);
          setResult(null);
          setError(null);
        } else {
          setError('Invalid image format');
        }
      };
      reader.onerror = () => {
        setError('Failed to read image file');
      };
      reader.readAsDataURL(file);
    }
  };

  const handleGenerate = async () => {
    if (!selectedImage) {
      setError("Please upload an image first");
      return;
    }

    if (!session) {
      toast.error("Please sign in to use this feature");
      router.push("/auth/signin");
      return;
    }

    try {
      setIsAnalyzing(true);
      setError(null);
      const response = await fetch("/api/analyze", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          image: selectedImage
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        if (errorData.error === "insufficient_credits") {
          toast.error("Insufficient credits. Please recharge.");
          router.push("/pricing");
          return;
        }
        throw new Error(errorData.error || "Failed to analyze image");
      }

      const data = await response.json();
      setResult(data);
      toast.success("Image generated successfully!");
    } catch (error) {
      console.error("Error:", error);
      
      // 检查错误信息是否包含NSFW关键词
      const errorMessage = error instanceof Error ? error.message : String(error);
      if (errorMessage.toLowerCase().includes('nsfw')) {
        setError("Sorry, AI content filter detected potentially sensitive content. Please try a different image.");
        toast.error("Content filter activated. Please try another image.");
        // 如果是NSFW错误，清除选择的图片以便用户可以选择新的
        setSelectedImage(null);
      } else {
        setError("Failed to analyze image. Please try again or use a different image.");
        toast.error("Failed to analyze image");
      }
    } finally {
      setIsAnalyzing(false);
    }
  };

  return (
    <Card className="w-full max-w-3xl mx-auto p-6 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="space-y-4">
        <div className="relative min-h-[300px] rounded-lg border-2 border-dashed border-muted-foreground/25 hover:border-muted-foreground/50 transition-colors">
          <input
            type="file"
            accept="image/*"
            onChange={handleImageUpload}
            className="absolute inset-0 w-full h-full opacity-0 cursor-pointer z-10"
            aria-label="Upload image"
          />
          <div className="absolute inset-0 flex flex-col items-center justify-center gap-2">
            {selectedImage ? (
              <div className="relative w-full h-full">
                <Image
                  src={selectedImage}
                  alt="Uploaded image"
                  className="object-contain"
                  fill
                  sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                  priority
                />
              </div>
            ) : (
              <div className="flex flex-col items-center gap-2 text-muted-foreground hover:text-muted-foreground/80 transition-colors">
                <Upload className="w-8 h-8" />
                <p className="text-sm font-medium">Upload Image</p>
                <p className="text-xs text-muted-foreground/70">Click or drag and drop</p>
              </div>
            )}
          </div>
        </div>
        <Button 
          className="w-full bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary/70 text-primary-foreground"
          size="lg"
          onClick={handleGenerate}
          disabled={!selectedImage || isAnalyzing}
        >
          <span className="flex items-center gap-2">
            {isAnalyzing ? (
              <>
                <svg className="animate-spin h-5 w-5" viewBox="0 0 24 24">
                  <circle
                    className="opacity-25"
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="currentColor"
                    strokeWidth="4"
                    fill="none"
                  />
                  <path
                    className="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                  />
                </svg>
                Analyzing...
              </>
            ) : (
              <>
                <svg
                  className="w-5 h-5"
                  viewBox="0 0 24 24"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M12 6V18M18 12L6 12"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
                Generate
              </>
            )}
          </span>
        </Button>
        
        {result && (
          <div className="space-y-4">
            <Button
              className="w-full bg-blue-600 hover:bg-blue-700 text-white"
              size="lg"
              onClick={async () => {
                try {
                  const urlToDownload = result.generatedImage;
                  if (!urlToDownload) {
                    throw new Error('No image URL available');
                  }

                  console.log('📥 Starting download for:', urlToDownload.substring(0, 50) + '...');

                  // Check if it's a base64 data URL (watermarked image)
                  if (urlToDownload.startsWith('data:image/')) {
                    console.log('📥 Downloading base64 watermarked image');
                    const link = document.createElement('a');
                    link.href = urlToDownload;
                    link.download = 'generated-image-watermarked.png';
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                    toast.success('Download started successfully!');
                  } else {
                    // For HTTP URLs, use proxy with download parameter
                    console.log('📥 Downloading HTTP image via proxy');
                    const proxyUrl = `/api/proxy-image?url=${encodeURIComponent(urlToDownload)}&download=true`;
                    const link = document.createElement('a');
                    link.href = proxyUrl;
                    link.download = 'generated-image.png';
                    link.target = '_blank';
                    document.body.appendChild(link);
                    link.click();

                    setTimeout(() => {
                      document.body.removeChild(link);
                    }, 1000);

                    toast.success('Download started successfully!');
                  }
                } catch (error) {
                  console.error('Error downloading image:', error);
                  toast.error('Failed to download image');

                  // Fallback: open in new window
                  if (result.generatedImage) {
                    window.open(result.generatedImage, '_blank');
                    toast.info("Please right-click on the image and select 'Save Image As...' to download");
                  }
                }
              }}
            >
              <span className="flex items-center gap-2">
                <svg 
                  className="w-5 h-5" 
                  fill="none" 
                  stroke="currentColor" 
                  viewBox="0 0 24 24" 
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path 
                    strokeLinecap="round" 
                    strokeLinejoin="round" 
                    strokeWidth={2} 
                    d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"
                  />
                </svg>
                Download Image
              </span>
            </Button>
            
            <div className="rounded-lg overflow-hidden">
              <div className="relative aspect-square w-full">
                <Image
                  src={result.generatedImage}
                  alt="Generated image"
                  className="object-contain"
                  fill
                  sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                />
              </div>
            </div>
          </div>
        )}

        {error && (
          <div className="p-4 rounded-lg bg-destructive/10">
            <p className="text-sm text-foreground/90">{error}</p>
          </div>
        )}
      </div>
    </Card>
  );
};
